require 'httparty'
require 'dotenv/load'

class RemakerTest
  include HTTParty
  base_uri "https://api.remaker.ai"

  def initialize(prompt:, style: "blackAndGrey", body_part: "arm", complexity: "medium")
    @prompt = prompt
    @style = style
    @body_part = body_part
    @complexity = complexity
  end

  def generate_tattoo
    response = self.class.post("/ai-tattoo/generate",
      headers: {
        "Authorization" => "Bearer #{ENV['REMAKER_API_KEY']}",
        "Content-Type" => "application/json"
      },
      body: {
        prompt: @prompt,
        style: @style,
        body_part: @body_part,
        complexity: @complexity
      }.to_json
    )

    if response.success?
      job_id = response.parsed_response["job_id"]
      puts "Job submitted successfully. Job ID: #{job_id}"
      return job_id
    else
      puts "Error generating tattoo: #{response.body}"
      return nil
    end
  end

  def check_status(job_id)
    response = self.class.get("/ai-tattoo/check-status/#{job_id}",
      headers: {
        "Authorization" => "Bearer #{ENV['REMAKER_API_KEY']}"
      }
    )

    if response.success?
      status = response.parsed_response["status"]
      puts "Status: #{status}"
      if status == "completed"
        puts "Image URL: #{response.parsed_response["images"].first["url"]}"
      else
        puts "Still processing. Try again later."
      end
    else
      puts "Error checking status: #{response.body}"
    end
  end
end

# -------- RUN THE SCRIPT --------
generator = RemakerTest.new(prompt: "a lion with a crown in minimalist style")
job_id = generator.generate_tattoo

if job_id
  puts "Waiting 10 seconds for processing..."
  sleep(10)
  generator.check_status(job_id)
end
